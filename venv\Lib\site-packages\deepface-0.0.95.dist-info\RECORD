../../README.md,sha256=EMYZmoEwGAz4i7u_7Vo3WInd7gd5LNYDSiBOXMJlVMQ,34451
../../Scripts/deepface.exe,sha256=BTM-emCAy3nK-rLWtMaQ_PCqFMzJkxq9S5UAFuqZrLA,108411
../../package_info.json,sha256=YoqFuZo3q396nyzDbQdwx8rBsMpiEKHaU3YIJMVSFoA,28
../../requirements.txt,sha256=AXfB7MJRdsbxyEGr5uh0IHkWtMdVu-AYrp8ABji-Djw,250
deepface-0.0.95.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
deepface-0.0.95.dist-info/LICENSE,sha256=c1nY3wN2iOn0Nzrt2d0pekZ3AwcqPJsAGxaeyM45VHc,1077
deepface-0.0.95.dist-info/METADATA,sha256=tDWQJSpvvAj4aAAedNn2Mdv9d46h2ZhqYFXJ3y0plVw,35504
deepface-0.0.95.dist-info/RECORD,,
deepface-0.0.95.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface-0.0.95.dist-info/WHEEL,sha256=G16H4A3IeoQmnOrYV4ueZGKSjhipXx8zc8nu9FGlvMA,92
deepface-0.0.95.dist-info/entry_points.txt,sha256=FtXy6R6kxpM46UZyI7o2xIOQwIyZT3_X7Rxiz02PBO0,51
deepface-0.0.95.dist-info/top_level.txt,sha256=AUkc4BVMe9KB4tfumnhBHQNANROa-is9C6k6hB6Ga0c,9
deepface/DeepFace.py,sha256=vpnrhhMAl4faw8cEqwiGomHjl6MO-0q7md9l7X2-ZKo,28030
deepface/__init__.py,sha256=7R4Ck_UYBQk7ExbeopdM1FD_fWjMgN3zGskUYDPCBeY,23
deepface/__pycache__/DeepFace.cpython-312.pyc,,
deepface/__pycache__/__init__.cpython-312.pyc,,
deepface/api/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface/api/__pycache__/__init__.cpython-312.pyc,,
deepface/api/src/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface/api/src/__pycache__/__init__.cpython-312.pyc,,
deepface/api/src/__pycache__/api.cpython-312.pyc,,
deepface/api/src/__pycache__/app.cpython-312.pyc,,
deepface/api/src/api.py,sha256=6GP2ROELKQHh36ixFUOf_WxQu9QuSn4lm8XrSbDWTvo,306
deepface/api/src/app.py,sha256=5bp1bOlOdjJV0TzV04lxfA7x89ALc4bfCL_qWYoqUWc,434
deepface/api/src/modules/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface/api/src/modules/__pycache__/__init__.cpython-312.pyc,,
deepface/api/src/modules/core/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface/api/src/modules/core/__pycache__/__init__.cpython-312.pyc,,
deepface/api/src/modules/core/__pycache__/routes.cpython-312.pyc,,
deepface/api/src/modules/core/__pycache__/service.cpython-312.pyc,,
deepface/api/src/modules/core/routes.py,sha256=VAL0qBZFRLYM3LfvnRYDyVFIpcYPWehDkii-zzoGqU0,5161
deepface/api/src/modules/core/service.py,sha256=T-dJY6gTW8pqjawaJEt0mwRzyzE66fRTKCznIPY1zQI,2713
deepface/commons/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface/commons/__pycache__/__init__.cpython-312.pyc,,
deepface/commons/__pycache__/constant.cpython-312.pyc,,
deepface/commons/__pycache__/folder_utils.cpython-312.pyc,,
deepface/commons/__pycache__/image_utils.cpython-312.pyc,,
deepface/commons/__pycache__/logger.cpython-312.pyc,,
deepface/commons/__pycache__/package_utils.cpython-312.pyc,,
deepface/commons/__pycache__/weight_utils.cpython-312.pyc,,
deepface/commons/constant.py,sha256=TYytDSMdbVyluBh_-7U7tITNTiKh6pxn_Fs72kAjLJE,117
deepface/commons/folder_utils.py,sha256=cXAh-xX9-36AoC2Hg84nDmBHgDgH8-2Qt0GWGMmdj0w,948
deepface/commons/image_utils.py,sha256=zzrUCl3PlHialhrd1Wqh3cKZGZZ-bilTOgX-C-SVD9k,6509
deepface/commons/logger.py,sha256=5Af5XcCsFoyL1XGgCDAv8vDtCruC9vIExyFyK-bxWqM,1882
deepface/commons/package_utils.py,sha256=uHONrR2kYbUOf6my74RrelQYXH1gAUivXerl8oY5QgE,1677
deepface/commons/weight_utils.py,sha256=XcQRuQ3wab24IKpPqjJid5xHPkUhk3D_Tt1DSQmubm8,7772
deepface/models/Demography.py,sha256=K_uqoayHoaTcu2M0D5W_HDwh5vPZbsVzzzp1dqH3YTQ,2525
deepface/models/Detector.py,sha256=JyeZwkpqADMtf3c8KA3elv5X6dfGgMidpyIjDnoaYmo,2454
deepface/models/FacialRecognition.py,sha256=X-Fs-upaYJObAipcjHQ7uF0dkZT4OWsolBUepyqzcHg,1714
deepface/models/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface/models/__pycache__/Demography.cpython-312.pyc,,
deepface/models/__pycache__/Detector.cpython-312.pyc,,
deepface/models/__pycache__/FacialRecognition.cpython-312.pyc,,
deepface/models/__pycache__/__init__.cpython-312.pyc,,
deepface/models/demography/Age.py,sha256=cSljgNzjTdMIpf816JcXWr0XdDKxknOOxjwBt0A1fCI,3445
deepface/models/demography/Emotion.py,sha256=HE-hUSJpgRVlMAwWRGiU7gTcJ7j5iyw3BfQN5Tdu1j0,3748
deepface/models/demography/Gender.py,sha256=w2e74qy0_cteOqM3sH3RaISH8XAL3DDfS9SXHJ75ii0,2754
deepface/models/demography/Race.py,sha256=jy5T9TGua3DIjYvYUO_w2GOuEm_51MSUOauanJU3Pq0,2756
deepface/models/demography/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface/models/demography/__pycache__/Age.cpython-312.pyc,,
deepface/models/demography/__pycache__/Emotion.cpython-312.pyc,,
deepface/models/demography/__pycache__/Gender.cpython-312.pyc,,
deepface/models/demography/__pycache__/Race.cpython-312.pyc,,
deepface/models/demography/__pycache__/__init__.cpython-312.pyc,,
deepface/models/face_detection/CenterFace.py,sha256=h8lwUf12Yeughow3bMTV3wFqOxZvdH1D_wfEyl72Uow,7200
deepface/models/face_detection/Dlib.py,sha256=qIpnNxcmkXOTC3DxsbXznYmspt_sy0-WTDrdQeSAc2I,3364
deepface/models/face_detection/FastMtCnn.py,sha256=9JtLjpaPYolMjW2xWXMXlkHgytDNBcO7EQNek_342A8,3039
deepface/models/face_detection/MediaPipe.py,sha256=szr7_YeEPxnvIPya3E508srz8jivKQxR26IQAeT9UMg,3426
deepface/models/face_detection/MtCnn.py,sha256=HW6xwWm-M3l9LoQI5vDpRQJfflEgmPavxJVAyAM3Y4o,1819
deepface/models/face_detection/OpenCv.py,sha256=wSif9M5YwNyUICRjPMJYMt9FY8is_ChNCvStL7Waplw,5973
deepface/models/face_detection/RetinaFace.py,sha256=6iFIgc3ZrjJ2_cgKDeItKBMil5CRnHw15JjXHvVgC-4,2390
deepface/models/face_detection/Ssd.py,sha256=wnzNTzyAoIpDi05grtQTnunPrlzy8RuyUEmP7lqJEw0,4449
deepface/models/face_detection/Yolo.py,sha256=K4yX7jEZeEwamb1HPPS4iD1ptASODkl34t2GEei83Zs,4316
deepface/models/face_detection/YuNet.py,sha256=Zw1dxs1yLzFvFTUCZBbiBFlMAd3JUKftlcDO6mdc5VA,4881
deepface/models/face_detection/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface/models/face_detection/__pycache__/CenterFace.cpython-312.pyc,,
deepface/models/face_detection/__pycache__/Dlib.cpython-312.pyc,,
deepface/models/face_detection/__pycache__/FastMtCnn.cpython-312.pyc,,
deepface/models/face_detection/__pycache__/MediaPipe.cpython-312.pyc,,
deepface/models/face_detection/__pycache__/MtCnn.cpython-312.pyc,,
deepface/models/face_detection/__pycache__/OpenCv.cpython-312.pyc,,
deepface/models/face_detection/__pycache__/RetinaFace.cpython-312.pyc,,
deepface/models/face_detection/__pycache__/Ssd.cpython-312.pyc,,
deepface/models/face_detection/__pycache__/Yolo.cpython-312.pyc,,
deepface/models/face_detection/__pycache__/YuNet.cpython-312.pyc,,
deepface/models/face_detection/__pycache__/__init__.cpython-312.pyc,,
deepface/models/facial_recognition/ArcFace.py,sha256=7gViJoFV8ZCT2f1oKQuCZI8zEzdM2DtVUqHK74B5jZY,4793
deepface/models/facial_recognition/Buffalo_L.py,sha256=x_cvKtbN0WmbA0vQH94eJvPStLJS3RGwM1gDIMcYKNY,3587
deepface/models/facial_recognition/DeepID.py,sha256=JGpvIgaZBdOahsu5WxpqOD4Ec7-eD81gWmI37-_y7mc,2434
deepface/models/facial_recognition/Dlib.py,sha256=HGPixL2JCtgvjGYcXfj7dowP1V8KzLDgkdZHQyUwy2E,2385
deepface/models/facial_recognition/Facenet.py,sha256=M4fJNRQKMoHC1a1EMMGKe_mJqr6Xc7SVQCt5Ij6jcnY,60860
deepface/models/facial_recognition/FbDeepFace.py,sha256=BBw8xwh_6Zl8PfcQXtKYMO2719dvsflkOlK4j-YVXyQ,3274
deepface/models/facial_recognition/GhostFaceNet.py,sha256=MNCWdD3GnSFMyiW2ke_wlQ6C-jr_kgz_a18fePhP9d4,9534
deepface/models/facial_recognition/OpenFace.py,sha256=qMXMLXLHNGXk0x6rwu91WPAXUpwa4QNz2KICr0BVwVg,17131
deepface/models/facial_recognition/SFace.py,sha256=jadZIyAQJBnQXT-JbAf6x075sjwjYA4EsFmlGp74C7s,2467
deepface/models/facial_recognition/VGGFace.py,sha256=ckYn-KIb3aN-pzjb5fGJ8CAjNJYGPTxzI9wwli8pdnE,5657
deepface/models/facial_recognition/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface/models/facial_recognition/__pycache__/ArcFace.cpython-312.pyc,,
deepface/models/facial_recognition/__pycache__/Buffalo_L.cpython-312.pyc,,
deepface/models/facial_recognition/__pycache__/DeepID.cpython-312.pyc,,
deepface/models/facial_recognition/__pycache__/Dlib.cpython-312.pyc,,
deepface/models/facial_recognition/__pycache__/Facenet.cpython-312.pyc,,
deepface/models/facial_recognition/__pycache__/FbDeepFace.cpython-312.pyc,,
deepface/models/facial_recognition/__pycache__/GhostFaceNet.cpython-312.pyc,,
deepface/models/facial_recognition/__pycache__/OpenFace.cpython-312.pyc,,
deepface/models/facial_recognition/__pycache__/SFace.cpython-312.pyc,,
deepface/models/facial_recognition/__pycache__/VGGFace.cpython-312.pyc,,
deepface/models/facial_recognition/__pycache__/__init__.cpython-312.pyc,,
deepface/models/spoofing/FasNet.py,sha256=2RmljMiAZV5oLUIKsQ2XbyTqS6wuEKuaQnwKU65-mR0,7329
deepface/models/spoofing/FasNetBackbone.py,sha256=fsRX_94RSH-F24leYZ-MCYUxEoS7MZnE1ZBVP94gNHQ,14352
deepface/models/spoofing/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface/models/spoofing/__pycache__/FasNet.cpython-312.pyc,,
deepface/models/spoofing/__pycache__/FasNetBackbone.cpython-312.pyc,,
deepface/models/spoofing/__pycache__/__init__.cpython-312.pyc,,
deepface/modules/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface/modules/__pycache__/__init__.cpython-312.pyc,,
deepface/modules/__pycache__/demography.cpython-312.pyc,,
deepface/modules/__pycache__/detection.cpython-312.pyc,,
deepface/modules/__pycache__/modeling.cpython-312.pyc,,
deepface/modules/__pycache__/preprocessing.cpython-312.pyc,,
deepface/modules/__pycache__/recognition.cpython-312.pyc,,
deepface/modules/__pycache__/representation.cpython-312.pyc,,
deepface/modules/__pycache__/streaming.cpython-312.pyc,,
deepface/modules/__pycache__/verification.cpython-312.pyc,,
deepface/modules/demography.py,sha256=guGqqOe6PsWrnlwA7HdZ9AgRFQWvzJTXlZkia8-7vUI,9913
deepface/modules/detection.py,sha256=R5du-ZFf0dfOFd6NL8W8hrtRUQulUvWjLSHrW0Of6j4,19722
deepface/modules/modeling.py,sha256=zwV9MDfVlRVQEMM1rT3oZXneqxVRPOJxd62_f_zlyVI,3464
deepface/modules/preprocessing.py,sha256=cTeY2apk7vPWyq5tZSFMdEggH2qLBF7z0dPq-ZeRAK0,3517
deepface/modules/recognition.py,sha256=b35rYXm51t0SR_MeyO6sUhQIOb2aHaerSpGGa5JDyC4,23732
deepface/modules/representation.py,sha256=iaZPl0rL20EpjcFAIoZ5uryVTKgHJxAh7JCtKYSlFZk,7274
deepface/modules/streaming.py,sha256=5hXdR9QKc5-GtUm2r8bU5kLTtOGKgGiU9Dqg5mZv3D4,35058
deepface/modules/verification.py,sha256=baWUfv1bICArOAhqtkpqWTnl5ntSHsqilWoP7IIgmXA,39386
