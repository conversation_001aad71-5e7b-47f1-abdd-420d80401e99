# Copyright 2023 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

class BufferedInputStream:
    def __init__(self, filename: str, buffer_size: int, token: TransactionToken = ...) -> None: ...
    def read(self, arg0: int) -> bytes: ...
    def readline(self) -> bytes: ...
    def seek(self, arg0: int) -> None: ...
    def tell(self) -> int: ...

class FileStatistics:
    def __init__(self, *args, **kwargs) -> None: ...
    @property
    def is_directory(self) -> bool: ...
    @property
    def length(self) -> int: ...
    @property
    def mtime_nsec(self) -> int: ...

class TransactionToken:
    def __init__(self, *args, **kwargs) -> None: ...

class WritableFile:
    def __init__(self, filename: str, mode: str, token: TransactionToken = ...) -> None: ...
    def append(self, arg0: str) -> None: ...
    def close(self) -> None: ...
    def flush(self) -> None: ...
    def tell(self) -> int: ...

def CopyFile(src: str, target: str, overwrite: bool, token: TransactionToken = ...) -> None: ...
def CreateDir(dirname: str, token: TransactionToken = ...) -> None: ...
def DeleteFile(filename: str, token: TransactionToken = ...) -> None: ...
def DeleteRecursively(dirname: str, token: TransactionToken = ...) -> None: ...
def FileExists(filename: str, token: TransactionToken = ...) -> None: ...
def GetChildren(dirname: str, token: TransactionToken = ...) -> list[str]: ...
def GetMatchingFiles(pattern: str, token: TransactionToken = ...) -> list[str]: ...
def GetRegisteredSchemes() -> list[str]: ...
def HasAtomicMove(arg0: str) -> bool: ...
def IsDirectory(dirname: str, token: TransactionToken = ...) -> bool: ...
def ReadFileToString(filename: str, token: TransactionToken = ...) -> bytes: ...
def RecursivelyCreateDir(dirname: str, token: TransactionToken = ...) -> None: ...
def RenameFile(src: str, target: str, overwrite: bool, token: TransactionToken = ...) -> None: ...
def Stat(filename: str, token: TransactionToken = ...) -> FileStatistics: ...
def WriteStringToFile(filename: str, data: str, token: TransactionToken = ...) -> None: ...
